#!/usr/bin/env python3

from fastapi import FastAPI
import uvicorn

app = FastAPI(title="Minimal Test App")

@app.get("/")
def read_root():
    return {"message": "Hello World", "status": "working"}

@app.get("/health")
def health_check():
    return {"status": "healthy", "message": "Minimal app is running"}

if __name__ == "__main__":
    print("Starting minimal FastAPI server...")
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
