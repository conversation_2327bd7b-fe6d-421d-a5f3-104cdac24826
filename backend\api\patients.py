from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Any, Optional
from datetime import datetime
from database.models import PatientDemographics, SymptomAssessment, ClinicalHistory, LabResults
from database.connection import SurrealDBManager
from security.auth_manager import get_current_active_user
from security.pid_manager import SecurePIDHandler
from utils.validators import validate_pid_format, validate_symptom_data, validate_lab_results
from utils.logging import logger

router = APIRouter(prefix="/patients", tags=["patients"])

# Dependency to get database manager
async def get_db():
    db = SurrealDBManager()
    await db.connect()
    try:
        yield db
    finally:
        await db.disconnect()

# Dependency to get PID handler
def get_pid_handler():
    return SecurePIDHandler()

class PatientCreateRequest(BaseModel):
    original_patient_id: str
    demographics: PatientDemographics
    clinic_key: Optional[str] = None

class PatientResponse(BaseModel):
    pid: str
    demographics: Dict[str, Any]
    symptoms: Dict[str, Any] = {}
    clinical_history: Dict[str, Any] = {}
    created_at: datetime
    updated_at: datetime
    clinician_id: str
    completeness_score: float

class SymptomUpdateRequest(BaseModel):
    pid: str
    symptoms: Dict[str, Any]

class HistoryUpdateRequest(BaseModel):
    pid: str
    clinical_history: Dict[str, Any]

class LabResultsRequest(BaseModel):
    pid: str
    lab_results: LabResults

@router.post("/create", response_model=Dict[str, str])
async def create_patient(
    request: PatientCreateRequest,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    db: SurrealDBManager = Depends(get_db),
    pid_handler: SecurePIDHandler = Depends(get_pid_handler)
):
    """Create a new patient record with secure PID"""
    
    try:
        # Generate secure PID
        pid = pid_handler.generate_pid(request.original_patient_id, request.clinic_key)
        search_token = pid_handler.generate_search_token(pid)
        
        # Validate PID format
        if not validate_pid_format(pid):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Generated PID format is invalid"
            )
        
        # Check if patient already exists
        existing_patient = await db.get_patient(pid)
        if existing_patient:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Patient with this PID already exists"
            )
        
        # Prepare patient data
        patient_data = {
            "pid": pid,
            "search_token": search_token,
            "demographics": request.demographics.model_dump(),
            "symptoms": {},
            "clinical_history": {},
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "clinician_id": current_user["user_id"],
            "is_locked": False,
            "completeness_score": 0.2,  # Initial score for demographics only
            "ml_features_cache": {}
        }
        
        # Create patient record
        patient_id = await db.create_patient(patient_data)
        
        logger.info(f"Patient created with PID: {pid} by clinician: {current_user['username']}")
        
        return {
            "pid": pid,
            "patient_id": patient_id,
            "message": "Patient created successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to create patient: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create patient: {str(e)}"
        )

@router.get("/{pid}", response_model=PatientResponse)
async def get_patient(
    pid: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    db: SurrealDBManager = Depends(get_db),
    pid_handler: SecurePIDHandler = Depends(get_pid_handler)
):
    """Retrieve patient by PID"""
    
    # Validate PID format
    if not pid_handler.validate_pid_integrity(pid):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid PID format"
        )
    
    # Get patient data
    patient = await db.get_patient(pid)
    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found"
        )
    
    # Check access permissions (clinician can only access their own patients)
    if patient.get("clinician_id") != current_user["user_id"] and "admin" not in current_user.get("roles", []):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this patient record"
        )
    
    return PatientResponse(**patient)

@router.put("/{pid}/symptoms")
async def update_symptoms(
    pid: str,
    request: SymptomUpdateRequest,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    db: SurrealDBManager = Depends(get_db),
    pid_handler: SecurePIDHandler = Depends(get_pid_handler)
):
    """Update patient symptoms"""

    # Validate PID
    if not pid_handler.validate_pid_integrity(pid):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid PID format"
        )

    # Validate symptom data
    validation_errors = validate_symptom_data(request.symptoms)
    if validation_errors:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Symptom validation errors: {', '.join(validation_errors)}"
        )

    # Get existing patient
    patient = await db.get_patient(pid)
    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found"
        )

    # Check permissions
    if patient.get("clinician_id") != current_user["user_id"] and "admin" not in current_user.get("roles", []):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this patient record"
        )

    # Update symptoms
    update_data = {
        "symptoms": request.symptoms,
        "updated_at": datetime.now(),
        "completeness_score": _calculate_completeness_score(
            patient.get("demographics", {}),
            request.symptoms,
            patient.get("clinical_history", {})
        )
    }

    success = await db.update_patient(pid, update_data)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update patient symptoms"
        )

    logger.info(f"Symptoms updated for patient {pid} by {current_user['username']}")
    return {"message": "Symptoms updated successfully"}

@router.put("/{pid}/history")
async def update_clinical_history(
    pid: str,
    request: HistoryUpdateRequest,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    db: SurrealDBManager = Depends(get_db),
    pid_handler: SecurePIDHandler = Depends(get_pid_handler)
):
    """Update patient clinical history"""

    # Validate PID
    if not pid_handler.validate_pid_integrity(pid):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid PID format"
        )

    # Get existing patient
    patient = await db.get_patient(pid)
    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Patient not found"
        )

    # Check permissions
    if patient.get("clinician_id") != current_user["user_id"] and "admin" not in current_user.get("roles", []):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this patient record"
        )

    # Update clinical history
    update_data = {
        "clinical_history": request.clinical_history,
        "updated_at": datetime.now(),
        "completeness_score": _calculate_completeness_score(
            patient.get("demographics", {}),
            patient.get("symptoms", {}),
            request.clinical_history
        )
    }

    success = await db.update_patient(pid, update_data)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update clinical history"
        )

    logger.info(f"Clinical history updated for patient {pid} by {current_user['username']}")
    return {"message": "Clinical history updated successfully"}

@router.get("/search")
async def search_patients(
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    db: SurrealDBManager = Depends(get_db)
):
    """Search patients with filters"""

    filters = {"clinician_id": current_user["user_id"]}

    if date_from:
        try:
            filters["date_from"] = datetime.fromisoformat(date_from)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date_from format. Use ISO format (YYYY-MM-DD)"
            )

    if date_to:
        try:
            filters["date_to"] = datetime.fromisoformat(date_to)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date_to format. Use ISO format (YYYY-MM-DD)"
            )

    patients = await db.search_patients(filters)

    # Remove sensitive data for list view
    patient_summaries = []
    for patient in patients:
        summary = {
            "pid": patient["pid"],
            "created_at": patient["created_at"],
            "updated_at": patient["updated_at"],
            "completeness_score": patient.get("completeness_score", 0),
            "is_locked": patient.get("is_locked", False)
        }
        patient_summaries.append(summary)

    return {"patients": patient_summaries, "total": len(patient_summaries)}

def _calculate_completeness_score(demographics: Dict, symptoms: Dict, history: Dict) -> float:
    """Calculate data completeness score (0-1)"""
    score = 0.0

    # Demographics (30% weight)
    if demographics:
        demo_fields = ['age_group', 'gender', 'education', 'occupation']
        demo_score = sum(1 for field in demo_fields if demographics.get(field)) / len(demo_fields)
        score += demo_score * 0.3

    # Symptoms (40% weight)
    if symptoms:
        symptom_domains = ['mood_symptoms', 'psychotic_symptoms', 'anxiety_symptoms']
        domain_scores = []
        for domain in symptom_domains:
            if domain in symptoms:
                domain_symptoms = symptoms[domain]
                if isinstance(domain_symptoms, dict) and domain_symptoms:
                    domain_scores.append(1.0)
                else:
                    domain_scores.append(0.0)
            else:
                domain_scores.append(0.0)

        if domain_scores:
            symptom_score = sum(domain_scores) / len(domain_scores)
            score += symptom_score * 0.4

    # Clinical history (30% weight)
    if history:
        history_fields = ['previous_diagnoses', 'medication_trials', 'family_psychiatric_history']
        history_score = sum(1 for field in history_fields if history.get(field)) / len(history_fields)
        score += history_score * 0.3

    return min(1.0, score)
