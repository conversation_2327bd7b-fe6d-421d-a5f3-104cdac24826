#!/usr/bin/env python3

print("Testing Python imports...")

try:
    import sys
    print("✓ sys imported")
    
    import os
    print("✓ os imported")
    
    # Test if we can import basic packages
    try:
        import fastapi
        print("✓ FastAPI imported")
    except ImportError as e:
        print(f"✗ FastAPI not available: {e}")
    
    try:
        import uvicorn
        print("✓ Uvicorn imported")
    except ImportError as e:
        print(f"✗ Uvicorn not available: {e}")
    
    try:
        import pydantic
        print("✓ Pydantic imported")
    except ImportError as e:
        print(f"✗ Pydantic not available: {e}")
    
    # Test our backend modules
    sys.path.append('./backend')
    
    try:
        from config import settings
        print("✓ Config imported")
        print(f"  App name: {settings.app_name}")
    except ImportError as e:
        print(f"✗ Config import failed: {e}")
    
    try:
        from utils.logging import setup_logging
        print("✓ Logging utils imported")
    except ImportError as e:
        print(f"✗ Logging utils import failed: {e}")
    
    print("\nImport test completed!")
    
except Exception as e:
    print(f"Error during import test: {e}")
    import traceback
    traceback.print_exc()
