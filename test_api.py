#!/usr/bin/env python3

import requests
import json

def test_api():
    base_url = "http://localhost:8000"
    
    print("Testing FastAPI server...")
    
    try:
        # Test health endpoint
        print("Testing health endpoint...")
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"Health check status: {response.status_code}")
        if response.status_code == 200:
            print(f"Health response: {response.json()}")
        else:
            print(f"Health check failed: {response.text}")
        
        # Test root endpoint
        print("\nTesting root endpoint...")
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"Root status: {response.status_code}")
        if response.status_code == 200:
            print(f"Root response: {response.json()}")
        
        # Test docs endpoint
        print("\nTesting docs endpoint...")
        response = requests.get(f"{base_url}/docs", timeout=5)
        print(f"Docs status: {response.status_code}")
        
        print("\n✓ API server is running and responding!")
        
    except requests.exceptions.ConnectionError:
        print("✗ Could not connect to the API server")
        print("Make sure the server is running on http://localhost:8000")
    except requests.exceptions.Timeout:
        print("✗ Request timed out")
    except Exception as e:
        print(f"✗ Error testing API: {e}")

if __name__ == "__main__":
    test_api()
